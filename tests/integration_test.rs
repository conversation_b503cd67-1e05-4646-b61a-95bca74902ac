mod helpers;

use helpers::*;
use std::time::Instant;
use std::fs;

#[test]
fn test_all_shred_files_in_data_directory() {
    // Scan tests/data directory and test all .bin files
    let data_dir = "tests/data";
    
    // Read all files in the data directory
    let entries = fs::read_dir(data_dir)
        .expect("Failed to read tests/data directory");
    
    let mut all_files = Vec::new();
    for entry in entries {
        let entry = entry.expect("Failed to read directory entry");
        let path = entry.path();
        
        if path.is_file() && path.extension().map_or(false, |ext| ext == "bin") {
            if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                all_files.push(filename.to_string());
            }
        }
    }
    
    // Sort files for consistent ordering
    all_files.sort();
    
    println!("Found {} .bin files in {}", all_files.len(), data_dir);
    
    let mut passed = 0;
    let mut failed = 0;
    let mut failed_files = Vec::new();
    
    let start_time = Instant::now();
    
    for (index, filename) in all_files.iter().enumerate() {
        if index % 100 == 0 {
            println!("Progress: {}/{} files tested", index, all_files.len());
        }
        
        match load_test_shred(filename) {
            Ok(shred_bytes) => {
                match test_parsed_entry_round_trip(&shred_bytes) {
                    Ok(true) => {
                        passed += 1;
                    }
                    Ok(false) => {
                        failed += 1;
                        failed_files.push(filename.clone());
                        eprintln!("❌ {} failed validation", filename);
                    }
                    Err(e) => {
                        failed += 1;
                        failed_files.push(filename.clone());
                        eprintln!("❌ {} error: {}", filename, e);
                    }
                }
            }
            Err(e) => {
                failed += 1;
                failed_files.push(filename.clone());
                eprintln!("❌ Failed to load {}: {}", filename, e);
            }
        }
    }
    
    let duration = start_time.elapsed();
    
    println!("\n=== COMPREHENSIVE TEST RESULTS ===");
    println!("Total files: {}", all_files.len());
    println!("Passed: {}", passed);
    println!("Failed: {}", failed);
    println!("Success rate: {:.2}%", (passed as f64 / all_files.len() as f64) * 100.0);
    println!("Duration: {:?}", duration);
    
    if passed > 0 {
        let avg_duration = duration / passed as u32;
        let total_bytes: usize = all_files.iter()
            .filter_map(|f| load_test_shred(f).ok())
            .map(|bytes| bytes.len())
            .sum();
        let throughput = total_bytes as f64 / duration.as_secs_f64();
        
        println!("Average duration per file: {:?}", avg_duration);
        println!("Total bytes processed: {}", total_bytes);
        println!("Throughput: {:.2} bytes/sec", throughput);
    }
    
    if !failed_files.is_empty() {
        println!("\nFailed files:");
        for file in &failed_files {
            println!("  - {}", file);
        }
    }
    
    assert_eq!(failed, 0, "All shred files should pass round-trip test");
}