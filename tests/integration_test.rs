mod helpers;

use helpers::*;
use std::time::Instant;

#[test]
fn test_basic_round_trip_with_sample_data() {
    // Test with synthetic data first
    let test_data = create_test_entry_data();
    let result = test_parsed_entry_round_trip(&test_data);
    
    assert!(result.is_ok(), "Basic round-trip test should succeed");
    assert!(result.unwrap(), "Basic round-trip test should pass validation");
}

#[test]
fn test_single_shred_file() {
    // Test with the first real shred file
    let filename = "shred_000001.bin";
    
    let shred_bytes = load_test_shred(filename)
        .expect(&format!("Failed to load {}", filename));
    
    let result = test_parsed_entry_round_trip(&shred_bytes)
        .expect(&format!("Round-trip test failed for {}", filename));
    
    assert!(result, "Round-trip test should pass for {}", filename);
    println!("✅ {} passed round-trip test", filename);
}

#[test]
fn test_sample_shred_files() {
    // Test with a representative sample of files
    let sample_files = get_sample_test_files();
    let mut passed = 0;
    let mut failed = 0;
    
    for filename in sample_files {
        println!("Testing {}...", filename);
        
        match load_test_shred(&filename) {
            Ok(shred_bytes) => {
                match test_parsed_entry_round_trip(&shred_bytes) {
                    Ok(true) => {
                        passed += 1;
                        println!("✅ {} passed", filename);
                    }
                    Ok(false) => {
                        failed += 1;
                        eprintln!("❌ {} failed validation", filename);
                    }
                    Err(e) => {
                        failed += 1;
                        eprintln!("❌ {} error: {}", filename, e);
                    }
                }
            }
            Err(e) => {
                failed += 1;
                eprintln!("❌ Failed to load {}: {}", filename, e);
            }
        }
    }
    
    println!("\nSample test results: {} passed, {} failed", passed, failed);
    assert_eq!(failed, 0, "All sample files should pass round-trip test");
}

#[test]
#[ignore] // Use `cargo test -- --ignored` to run this comprehensive test
fn test_all_shred_files() {
    // Comprehensive test of all 1000 shred files
    let all_files = get_all_test_shred_files();
    let mut passed = 0;
    let mut failed = 0;
    let mut failed_files = Vec::new();
    
    let start_time = Instant::now();
    
    for (index, filename) in all_files.iter().enumerate() {
        if index % 100 == 0 {
            println!("Progress: {}/{} files tested", index, all_files.len());
        }
        
        match load_test_shred(filename) {
            Ok(shred_bytes) => {
                match test_parsed_entry_round_trip(&shred_bytes) {
                    Ok(true) => {
                        passed += 1;
                    }
                    Ok(false) => {
                        failed += 1;
                        failed_files.push(filename.clone());
                        eprintln!("❌ {} failed validation", filename);
                    }
                    Err(e) => {
                        failed += 1;
                        failed_files.push(filename.clone());
                        eprintln!("❌ {} error: {}", filename, e);
                    }
                }
            }
            Err(e) => {
                failed += 1;
                failed_files.push(filename.clone());
                eprintln!("❌ Failed to load {}: {}", filename, e);
            }
        }
    }
    
    let duration = start_time.elapsed();
    
    println!("\n=== COMPREHENSIVE TEST RESULTS ===");
    println!("Total files: {}", all_files.len());
    println!("Passed: {}", passed);
    println!("Failed: {}", failed);
    println!("Success rate: {:.2}%", (passed as f64 / all_files.len() as f64) * 100.0);
    println!("Duration: {:?}", duration);
    
    if !failed_files.is_empty() {
        println!("\nFailed files:");
        for file in &failed_files {
            println!("  - {}", file);
        }
    }
    
    assert_eq!(failed, 0, "All shred files should pass round-trip test");
}

#[test]
fn test_error_handling_invalid_data() {
    // Test with invalid data
    let invalid_data = vec![1, 2, 3, 4, 5]; // Invalid bincode data
    
    // Should fail gracefully
    let result = test_parsed_entry_round_trip(&invalid_data);
    assert!(result.is_err(), "Invalid data should cause an error");
}

#[test]
fn test_empty_data() {
    // Test with empty data
    let empty_data = vec![];
    
    // Should fail gracefully
    let result = test_parsed_entry_round_trip(&empty_data);
    assert!(result.is_err(), "Empty data should cause an error");
}

#[test]
fn test_performance_benchmark() {
    // Performance test with a few files
    let sample_files = get_sample_test_files();
    let mut total_duration = std::time::Duration::new(0, 0);
    let mut total_bytes = 0;
    
    for filename in &sample_files {
        let shred_bytes = load_test_shred(filename)
            .expect(&format!("Failed to load {}", filename));
        
        total_bytes += shred_bytes.len();
        
        let start = Instant::now();
        let result = test_parsed_entry_round_trip(&shred_bytes)
            .expect(&format!("Round-trip test failed for {}", filename));
        let duration = start.elapsed();
        
        total_duration += duration;
        
        assert!(result, "Performance test should pass for {}", filename);
        println!("⚡ {} processed in {:?} ({} bytes)", filename, duration, shred_bytes.len());
    }
    
    let avg_duration = total_duration / sample_files.len() as u32;
    let throughput = total_bytes as f64 / total_duration.as_secs_f64();
    
    println!("\n=== PERFORMANCE RESULTS ===");
    println!("Files tested: {}", sample_files.len());
    println!("Total bytes: {}", total_bytes);
    println!("Total duration: {:?}", total_duration);
    println!("Average duration per file: {:?}", avg_duration);
    println!("Throughput: {:.2} bytes/sec", throughput);
}

#[test]
fn test_data_integrity_detailed() {
    // Detailed test of the first file to ensure all fields are correctly preserved
    let filename = "shred_000001.bin";
    let shred_bytes = load_test_shred(filename)
        .expect(&format!("Failed to load {}", filename));
    
    // Step 1: Deserialize with Solana crates
    let original_entries = deserialize_with_solana_crates(&shred_bytes)
        .expect("Failed to deserialize with Solana crates");
    
    // Step 2: Deserialize with our decoder
    let parsed_entry = deserialize_with_our_decoder(&shred_bytes)
        .expect("Failed to deserialize with our decoder");
    
    // Step 3: Convert back to Solana structs
    let converted_entries = shredstream_decoder::utils::convert_parsed_entry_to_solana_entry(&parsed_entry)
        .expect("Failed to convert back to Solana structs");
    
    // Detailed assertions
    assert_eq!(original_entries.len(), converted_entries.len(), 
               "Entry count should be preserved");
    assert_eq!(original_entries.len(), parsed_entry.entries.len(), 
               "ParsedEntry should have correct entry count");
    
    for (i, (orig, conv)) in original_entries.iter().zip(converted_entries.iter()).enumerate() {
        assert_eq!(orig.num_hashes, conv.num_hashes, 
                   "Entry {} num_hashes should be preserved", i);
        assert_eq!(orig.hash, conv.hash, 
                   "Entry {} hash should be preserved", i);
        assert_eq!(orig.transactions.len(), conv.transactions.len(), 
                   "Entry {} transaction count should be preserved", i);
    }
    
    println!("✅ Detailed data integrity test passed for {}", filename);
    println!("   - {} entries processed", original_entries.len());
    println!("   - All fields correctly preserved");
}