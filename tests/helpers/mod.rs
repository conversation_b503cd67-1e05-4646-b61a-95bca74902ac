use shredstream_decoder::types::ParsedEntry;
use shredstream_decoder::{decode_entries_core, utils::convert_parsed_entry_to_solana_entry};
use solana_entry::entry::Entry as SolanaEntry;
use std::fs;
use std::path::Path;

/// Load test shred data from a file in tests/data directory
pub fn load_test_shred(filename: &str) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
  let path = Path::new("tests/data").join(filename);
  let bytes = fs::read(&path)?;
  Ok(bytes)
}

/// Deserialize shred data using Solana crates directly (canonical reference)
pub fn deserialize_with_solana_crates(
  bytes: &[u8],
) -> Result<Vec<SolanaEntry>, Box<dyn std::error::Error>> {
  let entries: Vec<SolanaEntry> = bincode::deserialize(bytes)?;
  Ok(entries)
}

/// Deserialize shred data using our decoder (decode_entries_core)
pub fn deserialize_with_our_decoder(
  bytes: &[u8],
) -> Result<ParsedEntry, Box<dyn std::error::Error>> {
  decode_entries_core(bytes)
}

/// Compare two Solana Entry structs field by field
pub fn compare_entries(original: &SolanaEntry, converted: &SolanaEntry) -> bool {
  // Compare num_hashes
  if original.num_hashes != converted.num_hashes {
    eprintln!(
      "num_hashes mismatch: {} != {}",
      original.num_hashes, converted.num_hashes
    );
    return false;
  }

  // Compare hash
  if original.hash != converted.hash {
    eprintln!("hash mismatch: {:?} != {:?}", original.hash, converted.hash);
    return false;
  }

  // Compare transactions count
  if original.transactions.len() != converted.transactions.len() {
    eprintln!(
      "transactions length mismatch: {} != {}",
      original.transactions.len(),
      converted.transactions.len()
    );
    return false;
  }

  // Compare each transaction
  for (i, (orig_tx, conv_tx)) in original
    .transactions
    .iter()
    .zip(converted.transactions.iter())
    .enumerate()
  {
    if !compare_versioned_transactions(orig_tx, conv_tx) {
      eprintln!("Transaction {} mismatch", i);
      return false;
    }
  }

  true
}

/// Compare two VersionedTransaction structs
fn compare_versioned_transactions(
  original: &solana_transaction::versioned::VersionedTransaction,
  converted: &solana_transaction::versioned::VersionedTransaction,
) -> bool {
  // Compare signatures
  if original.signatures.len() != converted.signatures.len() {
    eprintln!(
      "signatures length mismatch: {} != {}",
      original.signatures.len(),
      converted.signatures.len()
    );
    return false;
  }

  for (i, (orig_sig, conv_sig)) in original
    .signatures
    .iter()
    .zip(converted.signatures.iter())
    .enumerate()
  {
    if orig_sig != conv_sig {
      eprintln!("Signature {} mismatch: {:?} != {:?}", i, orig_sig, conv_sig);
      return false;
    }
  }

  // Compare messages
  if !compare_versioned_messages(&original.message, &converted.message) {
    eprintln!("Message mismatch");
    return false;
  }

  true
}

/// Compare two VersionedMessage structs
fn compare_versioned_messages(
  original: &solana_message::VersionedMessage,
  converted: &solana_message::VersionedMessage,
) -> bool {
  match (original, converted) {
    (
      solana_message::VersionedMessage::Legacy(orig),
      solana_message::VersionedMessage::Legacy(conv),
    ) => compare_legacy_messages(orig, conv),
    (solana_message::VersionedMessage::V0(orig), solana_message::VersionedMessage::V0(conv)) => {
      compare_v0_messages(orig, conv)
    }
    _ => {
      eprintln!(
        "Message type mismatch: {:?} != {:?}",
        std::mem::discriminant(original),
        std::mem::discriminant(converted)
      );
      false
    }
  }
}

/// Compare two Legacy Message structs
fn compare_legacy_messages(
  original: &solana_message::Message,
  converted: &solana_message::Message,
) -> bool {
  // Compare header
  if original.header != converted.header {
    eprintln!(
      "Header mismatch: {:?} != {:?}",
      original.header, converted.header
    );
    return false;
  }

  // Compare account keys
  if original.account_keys != converted.account_keys {
    eprintln!("Account keys mismatch");
    return false;
  }

  // Compare recent blockhash
  if original.recent_blockhash != converted.recent_blockhash {
    eprintln!(
      "Recent blockhash mismatch: {:?} != {:?}",
      original.recent_blockhash, converted.recent_blockhash
    );
    return false;
  }

  // Compare instructions
  if original.instructions.len() != converted.instructions.len() {
    eprintln!(
      "Instructions length mismatch: {} != {}",
      original.instructions.len(),
      converted.instructions.len()
    );
    return false;
  }

  for (i, (orig_inst, conv_inst)) in original
    .instructions
    .iter()
    .zip(converted.instructions.iter())
    .enumerate()
  {
    if !compare_compiled_instructions(orig_inst, conv_inst) {
      eprintln!("Instruction {} mismatch", i);
      return false;
    }
  }

  true
}

/// Compare two V0 Message structs
fn compare_v0_messages(
  original: &solana_message::v0::Message,
  converted: &solana_message::v0::Message,
) -> bool {
  // Compare header
  if original.header != converted.header {
    eprintln!(
      "V0 Header mismatch: {:?} != {:?}",
      original.header, converted.header
    );
    return false;
  }

  // Compare account keys
  if original.account_keys != converted.account_keys {
    eprintln!("V0 Account keys mismatch");
    return false;
  }

  // Compare recent blockhash
  if original.recent_blockhash != converted.recent_blockhash {
    eprintln!(
      "V0 Recent blockhash mismatch: {:?} != {:?}",
      original.recent_blockhash, converted.recent_blockhash
    );
    return false;
  }

  // Compare instructions
  if original.instructions.len() != converted.instructions.len() {
    eprintln!(
      "V0 Instructions length mismatch: {} != {}",
      original.instructions.len(),
      converted.instructions.len()
    );
    return false;
  }

  for (i, (orig_inst, conv_inst)) in original
    .instructions
    .iter()
    .zip(converted.instructions.iter())
    .enumerate()
  {
    if !compare_compiled_instructions(orig_inst, conv_inst) {
      eprintln!("V0 Instruction {} mismatch", i);
      return false;
    }
  }

  // Compare address table lookups
  if original.address_table_lookups.len() != converted.address_table_lookups.len() {
    eprintln!(
      "Address table lookups length mismatch: {} != {}",
      original.address_table_lookups.len(),
      converted.address_table_lookups.len()
    );
    return false;
  }

  for (i, (orig_lookup, conv_lookup)) in original
    .address_table_lookups
    .iter()
    .zip(converted.address_table_lookups.iter())
    .enumerate()
  {
    if !compare_address_table_lookups(orig_lookup, conv_lookup) {
      eprintln!("Address table lookup {} mismatch", i);
      return false;
    }
  }

  true
}

/// Compare two CompiledInstruction structs
fn compare_compiled_instructions(
  original: &solana_message::compiled_instruction::CompiledInstruction,
  converted: &solana_message::compiled_instruction::CompiledInstruction,
) -> bool {
  if original.program_id_index != converted.program_id_index {
    eprintln!(
      "program_id_index mismatch: {} != {}",
      original.program_id_index, converted.program_id_index
    );
    return false;
  }

  if original.accounts != converted.accounts {
    eprintln!(
      "accounts mismatch: {:?} != {:?}",
      original.accounts, converted.accounts
    );
    return false;
  }

  if original.data != converted.data {
    eprintln!("data mismatch: {:?} != {:?}", original.data, converted.data);
    return false;
  }

  true
}

/// Compare two MessageAddressTableLookup structs
fn compare_address_table_lookups(
  original: &solana_message::v0::MessageAddressTableLookup,
  converted: &solana_message::v0::MessageAddressTableLookup,
) -> bool {
  if original.account_key != converted.account_key {
    eprintln!(
      "lookup account_key mismatch: {:?} != {:?}",
      original.account_key, converted.account_key
    );
    return false;
  }

  if original.writable_indexes != converted.writable_indexes {
    eprintln!(
      "writable_indexes mismatch: {:?} != {:?}",
      original.writable_indexes, converted.writable_indexes
    );
    return false;
  }

  if original.readonly_indexes != converted.readonly_indexes {
    eprintln!(
      "readonly_indexes mismatch: {:?} != {:?}",
      original.readonly_indexes, converted.readonly_indexes
    );
    return false;
  }

  true
}

/// Compare two vectors of Solana Entry structs
pub fn compare_entry_vectors(original: &[SolanaEntry], converted: &[SolanaEntry]) -> bool {
  if original.len() != converted.len() {
    eprintln!(
      "Entry vectors length mismatch: {} != {}",
      original.len(),
      converted.len()
    );
    return false;
  }

  for (i, (orig_entry, conv_entry)) in original.iter().zip(converted.iter()).enumerate() {
    if !compare_entries(orig_entry, conv_entry) {
      eprintln!("Entry {} mismatch", i);
      return false;
    }
  }

  true
}

/// Perform complete round-trip test for ParsedEntry
pub fn test_parsed_entry_round_trip(
  shred_bytes: &[u8],
) -> Result<bool, Box<dyn std::error::Error>> {
  // Step 1: Deserialize with Solana crates (canonical reference)
  let original_entries = deserialize_with_solana_crates(shred_bytes)?;

  // Step 2: Deserialize with our decoder
  let parsed_entry = deserialize_with_our_decoder(shred_bytes)?;

  // Step 3: Convert back to Solana structs
  let converted_entries = convert_parsed_entry_to_solana_entry(&parsed_entry)?;

  // Step 4: Field-by-field comparison
  let is_equal = compare_entry_vectors(&original_entries, &converted_entries);

  if !is_equal {
    eprintln!("Round-trip test failed for shred data");
    eprintln!("Original entries count: {}", original_entries.len());
    eprintln!("Converted entries count: {}", converted_entries.len());
  }

  Ok(is_equal)
}

/// Create test data for unit testing (when real shred files are not available)
pub fn create_test_entry_data() -> Vec<u8> {
  let test_entries = vec![
    SolanaEntry {
      num_hashes: 1,
      hash: solana_hash::Hash::default(),
      transactions: vec![],
    },
    SolanaEntry {
      num_hashes: 5,
      hash: solana_hash::Hash::new_unique(),
      transactions: vec![],
    },
  ];

  bincode::serialize(&test_entries).expect("Failed to serialize test data")
}

/// Get all test shred filenames
pub fn get_all_test_shred_files() -> Vec<String> {
  (1..=1000).map(|i| format!("shred_{:06}.bin", i)).collect()
}

/// Test a subset of shred files for quick validation
pub fn get_sample_test_files() -> Vec<String> {
  vec![
    "shred_000001.bin".to_string(),
    "shred_000100.bin".to_string(),
    "shred_000250.bin".to_string(),
    "shred_000500.bin".to_string(),
    "shred_000750.bin".to_string(),
    "shred_001000.bin".to_string(),
  ]
}
