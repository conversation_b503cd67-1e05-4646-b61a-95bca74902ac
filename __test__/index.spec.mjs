import test from 'ava'
import { readFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

import { decodeEntries } from '../index.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Helper function to load test data
function loadTestData(filename) {
  const filePath = join(__dirname, filename)
  return readFileSync(filePath)
}

test('decodeEntries function exists', (t) => {
  t.is(typeof decodeEntries, 'function')
})

test('decodeEntries with sample shred data', (t) => {
  const shredData = loadTestData('sample_shred.bin')
  
  // Should not throw
  t.notThrows(() => {
    const result = decodeEntries(shredData)
    t.truthy(result)
  })
})

test('decodeEntries returns correct structure', (t) => {
  const shredData = loadTestData('sample_shred.bin')
  const result = decodeEntries(shredData)
  
  // Check top-level structure
  t.true(typeof result === 'object')
  t.true('entries' in result)
  t.true(Array.isArray(result.entries))
  
  // Check entries array is not empty
  t.true(result.entries.length > 0)
  
  // Check first entry structure
  const firstEntry = result.entries[0]
  t.true(typeof firstEntry === 'object')
  t.true('numHashes' in firstEntry)
  t.true('hash' in firstEntry)
  t.true('transactions' in firstEntry)
  
  // Check types
  t.is(typeof firstEntry.numHashes, 'number')
  t.true(typeof firstEntry.hash === 'object')
  t.true(Array.isArray(firstEntry.transactions))
  
  // Check hash structure
  t.true('bytes' in firstEntry.hash)
  t.true(Array.isArray(firstEntry.hash.bytes))
  t.is(firstEntry.hash.bytes.length, 32) // Hash should be 32 bytes
  
  // Validate hash bytes are numbers in valid range
  firstEntry.hash.bytes.forEach(byte => {
    t.is(typeof byte, 'number')
    t.true(byte >= 0 && byte <= 255)
  })
})

test('decodeEntries validates transaction structure', (t) => {
  const shredData = loadTestData('sample_shred.bin')
  const result = decodeEntries(shredData)
  
  // If there are transactions, validate their structure
  result.entries.forEach((entry, entryIndex) => {
    entry.transactions.forEach((transaction, txIndex) => {
      // Check transaction structure
      t.true(typeof transaction === 'object', `Entry ${entryIndex}, Transaction ${txIndex} should be object`)
      t.true('signatures' in transaction, `Entry ${entryIndex}, Transaction ${txIndex} should have signatures`)
      t.true('message' in transaction, `Entry ${entryIndex}, Transaction ${txIndex} should have message`)
      
      // Check signatures
      t.true(Array.isArray(transaction.signatures), `Entry ${entryIndex}, Transaction ${txIndex} signatures should be array`)
      
      // Validate each signature
      transaction.signatures.forEach((signature, sigIndex) => {
        t.true(typeof signature === 'object', `Entry ${entryIndex}, Transaction ${txIndex}, Signature ${sigIndex} should be object`)
        t.true('bytes' in signature, `Entry ${entryIndex}, Transaction ${txIndex}, Signature ${sigIndex} should have bytes`)
        t.true(Array.isArray(signature.bytes), `Entry ${entryIndex}, Transaction ${txIndex}, Signature ${sigIndex} bytes should be array`)
        t.is(signature.bytes.length, 64, `Entry ${entryIndex}, Transaction ${txIndex}, Signature ${sigIndex} should be 64 bytes`)
        
        // Validate signature bytes
        signature.bytes.forEach(byte => {
          t.is(typeof byte, 'number')
          t.true(byte >= 0 && byte <= 255)
        })
      })
      
      // Check message structure
      const message = transaction.message
      t.true(typeof message === 'object', `Entry ${entryIndex}, Transaction ${txIndex} message should be object`)
      t.true('messageType' in message, `Entry ${entryIndex}, Transaction ${txIndex} message should have messageType`)
      t.is(typeof message.messageType, 'string', `Entry ${entryIndex}, Transaction ${txIndex} messageType should be string`)
      
      // Check message type and corresponding structure
      if (message.messageType === 'Legacy') {
        t.true('legacy' in message, `Entry ${entryIndex}, Transaction ${txIndex} Legacy message should have legacy field`)
        t.true(typeof message.legacy === 'object', `Entry ${entryIndex}, Transaction ${txIndex} legacy should be object`)
        
        const legacyMsg = message.legacy
        t.true('header' in legacyMsg, `Entry ${entryIndex}, Transaction ${txIndex} legacy should have header`)
        t.true('accountKeys' in legacyMsg, `Entry ${entryIndex}, Transaction ${txIndex} legacy should have accountKeys`)
        t.true('recentBlockhash' in legacyMsg, `Entry ${entryIndex}, Transaction ${txIndex} legacy should have recentBlockhash`)
        t.true('instructions' in legacyMsg, `Entry ${entryIndex}, Transaction ${txIndex} legacy should have instructions`)
        
      } else if (message.messageType === 'V0') {
        t.true('v0' in message, `Entry ${entryIndex}, Transaction ${txIndex} V0 message should have v0 field`)
        t.true(typeof message.v0 === 'object', `Entry ${entryIndex}, Transaction ${txIndex} v0 should be object`)
        
        const v0Msg = message.v0
        t.true('header' in v0Msg, `Entry ${entryIndex}, Transaction ${txIndex} v0 should have header`)
        t.true('accountKeys' in v0Msg, `Entry ${entryIndex}, Transaction ${txIndex} v0 should have accountKeys`)
        t.true('recentBlockhash' in v0Msg, `Entry ${entryIndex}, Transaction ${txIndex} v0 should have recentBlockhash`)
        t.true('instructions' in v0Msg, `Entry ${entryIndex}, Transaction ${txIndex} v0 should have instructions`)
        t.true('addressTableLookups' in v0Msg, `Entry ${entryIndex}, Transaction ${txIndex} v0 should have addressTableLookups`)
      }
    })
  })
})

test('decodeEntries with invalid data throws error', (t) => {
  const invalidData = Buffer.from([1, 2, 3, 4, 5]) // Invalid bincode data
  
  t.throws(() => {
    decodeEntries(invalidData)
  }, {
    instanceOf: Error
  })
})

test('decodeEntries with empty buffer throws error', (t) => {
  const emptyData = Buffer.alloc(0)
  
  t.throws(() => {
    decodeEntries(emptyData)
  }, {
    instanceOf: Error
  })
})

test('decodeEntries performance benchmark', (t) => {
  const shredData = loadTestData('sample_shred.bin')
  
  const iterations = 100
  const start = process.hrtime.bigint()
  
  for (let i = 0; i < iterations; i++) {
    decodeEntries(shredData)
  }
  
  const end = process.hrtime.bigint()
  const totalTimeMs = Number(end - start) / 1_000_000
  const avgTimeMs = totalTimeMs / iterations
  
  console.log(`Performance: ${iterations} iterations in ${totalTimeMs.toFixed(2)}ms`)
  console.log(`Average: ${avgTimeMs.toFixed(2)}ms per decode`)
  console.log(`Throughput: ${(1000 / avgTimeMs).toFixed(2)} decodes/second`)
  
  // Performance assertion - should decode in reasonable time
  t.true(avgTimeMs < 100, `Average decode time should be less than 100ms, got ${avgTimeMs.toFixed(2)}ms`)
  
  t.pass()
})

test('decodeEntries data consistency', (t) => {
  const shredData = loadTestData('sample_shred.bin')
  
  // Decode the same data multiple times
  const result1 = decodeEntries(shredData)
  const result2 = decodeEntries(shredData)
  const result3 = decodeEntries(shredData)
  
  // Results should be identical
  t.deepEqual(result1, result2)
  t.deepEqual(result2, result3)
  t.deepEqual(result1, result3)
})

test('decodeEntries memory usage', (t) => {
  const shredData = loadTestData('sample_shred.bin')
  
  // Get initial memory usage
  const initialMemory = process.memoryUsage()
  
  // Perform multiple decodes
  const results = []
  for (let i = 0; i < 50; i++) {
    results.push(decodeEntries(shredData))
  }
  
  // Force garbage collection if available
  if (global.gc) {
    global.gc()
  }
  
  const finalMemory = process.memoryUsage()
  const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
  
  console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB for 50 decodes`)
  console.log(`Average per decode: ${(memoryIncrease / 50 / 1024).toFixed(2)}KB`)
  
  // Memory increase should be reasonable
  t.true(memoryIncrease < 100 * 1024 * 1024, 'Memory increase should be less than 100MB')
  
  t.pass()
})