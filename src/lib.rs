#![deny(clippy::all)]

#[cfg(feature = "napi")]
use napi::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sul<PERSON>, <PERSON>};
use solana_entry::entry::Entry;

#[cfg(feature = "napi")]
#[macro_use]
extern crate napi_derive;

pub mod types;
pub mod utils;

use types::ParsedEntry;
use utils::convert_entries_to_parsed;

// Core logic without NAPI dependencies - accessible for both production and testing
pub fn decode_entries_core(
  bytes: &[u8],
) -> std::result::Result<ParsedEntry, Box<dyn std::error::Error>> {
  let entries: Vec<Entry> = bincode::deserialize(bytes)?;
  Ok(convert_entries_to_parsed(entries))
}

// NAPI wrapper for production use
#[cfg(feature = "napi")]
#[napi]
pub fn decode_entries(bytes: JsBuffer) -> Result<ParsedEntry> {
  decode_entries_core(&bytes.into_value()?)
    .map_err(|e| Error::new(Status::Gene<PERSON>F<PERSON>ure, e.to_string()))
}

#[cfg(test)]
mod tests {
  use super::*;

  #[test]
  fn test_decode_entries_core_function() {
    // Test with a simple case - create some test data
    let test_entries = vec![solana_entry::entry::Entry {
      num_hashes: 1,
      hash: solana_hash::Hash::default(),
      transactions: vec![],
    }];

    // Serialize the test data
    let serialized = bincode::serialize(&test_entries).expect("Failed to serialize test data");

    // Test our core decode function
    let result = decode_entries_core(&serialized);
    assert!(result.is_ok(), "decode_entries_core should succeed");

    let parsed = result.unwrap();
    assert_eq!(parsed.entries.len(), 1, "Should have 1 entry");
    assert_eq!(parsed.entries[0].num_hashes, 1.0, "num_hashes should match");
  }

  #[test]
  fn test_error_handling_invalid_data() {
    let invalid_data = vec![1, 2, 3, 4, 5]; // Invalid bincode data
    let result = decode_entries_core(&invalid_data);

    assert!(result.is_err(), "Should fail with invalid data");
  }
}
