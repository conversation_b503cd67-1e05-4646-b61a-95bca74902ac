#[cfg(feature = "napi")]
use napi_derive::napi;

#[cfg_attr(feature = "napi", napi(object))]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Hash {
  pub bytes: Vec<u8>,
}

impl Hash {
  pub fn new(bytes: Vec<u8>) -> Self {
    Self { bytes }
  }

  pub fn from_solana_hash(hash: &solana_hash::Hash) -> Self {
    Self {
      bytes: hash.to_bytes().to_vec(),
    }
  }

  pub fn to_solana_hash(&self) -> Result<solana_hash::Hash, String> {
    if self.bytes.len() != 32 {
      return Err(format!(
        "Invalid hash length: expected 32 bytes, got {}",
        self.bytes.len()
      ));
    }
    let mut array = [0u8; 32];
    array.copy_from_slice(&self.bytes);
    Ok(solana_hash::Hash::new_from_array(array))
  }
}
