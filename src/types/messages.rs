use crate::types::{Hash, Pubkey};

#[cfg(feature = "napi")]
use napi_derive::napi;

#[cfg_attr(feature = "napi", napi(object))]
#[derive(Debug, Clone)]
pub struct MessageHeader {
  pub num_required_signatures: u32,
  pub num_readonly_signed_accounts: u32,
  pub num_readonly_unsigned_accounts: u32,
}

impl MessageHeader {
  pub fn from_solana_header(header: &solana_message::MessageHeader) -> Self {
    Self {
      num_required_signatures: header.num_required_signatures as u32,
      num_readonly_signed_accounts: header.num_readonly_signed_accounts as u32,
      num_readonly_unsigned_accounts: header.num_readonly_unsigned_accounts as u32,
    }
  }

  pub fn to_solana_header(&self) -> solana_message::MessageHeader {
    solana_message::MessageHeader {
      num_required_signatures: self.num_required_signatures as u8,
      num_readonly_signed_accounts: self.num_readonly_signed_accounts as u8,
      num_readonly_unsigned_accounts: self.num_readonly_unsigned_accounts as u8,
    }
  }
}

#[cfg_attr(feature = "napi", napi(object))]
#[derive(Debug, Clone)]
pub struct CompiledInstruction {
  pub program_id_index: u32,
  pub accounts: Vec<u8>,
  pub data: Vec<u8>,
}

impl CompiledInstruction {
  pub fn from_solana_instruction(
    instruction: &solana_message::compiled_instruction::CompiledInstruction,
  ) -> Self {
    Self {
      program_id_index: instruction.program_id_index as u32,
      accounts: instruction.accounts.clone(),
      data: instruction.data.clone(),
    }
  }

  pub fn to_solana_instruction(&self) -> solana_message::compiled_instruction::CompiledInstruction {
    solana_message::compiled_instruction::CompiledInstruction {
      program_id_index: self.program_id_index as u8,
      accounts: self.accounts.clone(),
      data: self.data.clone(),
    }
  }
}

#[cfg_attr(feature = "napi", napi(object))]
#[derive(Debug, Clone)]
pub struct MessageAddressTableLookup {
  pub account_key: Pubkey,
  pub writable_indexes: Vec<u8>,
  pub readonly_indexes: Vec<u8>,
}

impl MessageAddressTableLookup {
  pub fn from_solana_lookup(lookup: &solana_message::v0::MessageAddressTableLookup) -> Self {
    Self {
      account_key: Pubkey::from_solana_pubkey(&lookup.account_key),
      writable_indexes: lookup.writable_indexes.clone(),
      readonly_indexes: lookup.readonly_indexes.clone(),
    }
  }

  pub fn to_solana_lookup(&self) -> Result<solana_message::v0::MessageAddressTableLookup, String> {
    Ok(solana_message::v0::MessageAddressTableLookup {
      account_key: self.account_key.to_solana_pubkey()?,
      writable_indexes: self.writable_indexes.clone(),
      readonly_indexes: self.readonly_indexes.clone(),
    })
  }
}

#[cfg_attr(feature = "napi", napi(object))]
#[derive(Debug, Clone)]
pub struct LegacyMessage {
  pub header: MessageHeader,
  pub account_keys: Vec<Pubkey>,
  pub recent_blockhash: Hash,
  pub instructions: Vec<CompiledInstruction>,
}

impl LegacyMessage {
  pub fn from_solana_message(message: &solana_message::Message) -> Self {
    Self {
      header: MessageHeader::from_solana_header(&message.header),
      account_keys: message
        .account_keys
        .iter()
        .map(|key| Pubkey::from_solana_pubkey(key))
        .collect(),
      recent_blockhash: Hash::from_solana_hash(&message.recent_blockhash),
      instructions: message
        .instructions
        .iter()
        .map(|inst| CompiledInstruction::from_solana_instruction(inst))
        .collect(),
    }
  }

  pub fn to_solana_message(&self) -> Result<solana_message::Message, String> {
    let account_keys: Result<Vec<_>, _> = self
      .account_keys
      .iter()
      .map(|key| key.to_solana_pubkey())
      .collect();

    Ok(solana_message::Message {
      header: self.header.to_solana_header(),
      account_keys: account_keys?,
      recent_blockhash: self.recent_blockhash.to_solana_hash()?,
      instructions: self
        .instructions
        .iter()
        .map(|inst| inst.to_solana_instruction())
        .collect(),
    })
  }
}

#[cfg_attr(feature = "napi", napi(object))]
#[derive(Debug, Clone)]
pub struct V0Message {
  pub header: MessageHeader,
  pub account_keys: Vec<Pubkey>,
  pub recent_blockhash: Hash,
  pub instructions: Vec<CompiledInstruction>,
  pub address_table_lookups: Vec<MessageAddressTableLookup>,
}

impl V0Message {
  pub fn from_solana_v0_message(message: &solana_message::v0::Message) -> Self {
    Self {
      header: MessageHeader::from_solana_header(&message.header),
      account_keys: message
        .account_keys
        .iter()
        .map(|key| Pubkey::from_solana_pubkey(key))
        .collect(),
      recent_blockhash: Hash::from_solana_hash(&message.recent_blockhash),
      instructions: message
        .instructions
        .iter()
        .map(|inst| CompiledInstruction::from_solana_instruction(inst))
        .collect(),
      address_table_lookups: message
        .address_table_lookups
        .iter()
        .map(|lookup| MessageAddressTableLookup::from_solana_lookup(lookup))
        .collect(),
    }
  }

  pub fn to_solana_v0_message(&self) -> Result<solana_message::v0::Message, String> {
    let account_keys: Result<Vec<_>, _> = self
      .account_keys
      .iter()
      .map(|key| key.to_solana_pubkey())
      .collect();

    let address_table_lookups: Result<Vec<_>, _> = self
      .address_table_lookups
      .iter()
      .map(|lookup| lookup.to_solana_lookup())
      .collect();

    Ok(solana_message::v0::Message {
      header: self.header.to_solana_header(),
      account_keys: account_keys?,
      recent_blockhash: self.recent_blockhash.to_solana_hash()?,
      instructions: self
        .instructions
        .iter()
        .map(|inst| inst.to_solana_instruction())
        .collect(),
      address_table_lookups: address_table_lookups?,
    })
  }
}
